import pandas as pd
import json
import ast

csv_file = 'G_locative.csv'

try:
    df_raw = pd.read_csv(csv_file)
    print(f"Fichier CSV chargé avec succès: {csv_file}")    
    print("Aperçu des données brutes:")
    display(df_raw.head())
    
except FileNotFoundError:
    print(f"Erreur: Fichier {csv_file} non trouvé")
    print("Vérifiez le chemin du fichier et réessayez")
except Exception as e:
    print(f"Erreur lors du chargement: {str(e)}")

def parse_json_data(json_str):
        parsed = ast.literal_eval(json_str)
        print("Parsing réussi")
        return parsed

# Parsing des données
parsed_data = parse_json_data(df_raw['data'].iloc[0])

df_locative = pd.DataFrame(parsed_data)
print(f"DataFrame créé avec {len(df_locative)} biens locatifs")

# Affichage des colonnes disponibles
print("\nColonnes disponibles:")
print(df_locative.columns.tolist())

# Affichage des données de base
print("\n Aperçu des données:")
display(df_locative.head())

class Cleaning:
    def __init__(self, df):
        self.df = df.copy()

    def clean(self):
        # Supprimer colonnes inutiles
        self.df.drop(columns=['id', 'created_at'], inplace=True)
        # Renommer colonnes
        self.df.rename(columns={
            'libelle': 'Bien',
            'adresse': 'Adresse',
            'type_id': 'Type ID',
            'proprietaires': 'Proprietaires',
            'locataires': 'Locataires',
            'charges': 'Charges',
            'totalCharges': 'Total Charges',
            'totaLoyer': 'Total Loyer',
            'totalImpayer': 'Total Impaye',
            'type': 'Type',
            'actifs': 'Actifs'
        }, inplace=True)
        return self.df
cleaner = Cleaning(df_locative)
df_clean = cleaner.clean()
df_clean.head()

# 4. Lecture du CSV sauvegardé
df_loaded = pd.read_csv(csv_filepath)

print(f"CSV rechargé: {df_loaded.shape}")
print("Aperçu des données:")
df_loaded.head()

# 5. Extraction des données pour visualisation
biens = []
for _, row in df_loaded.iterrows():
    # Compter les propriétaires et locataires
    proprietaires = eval(row['proprietaires']) if pd.notna(row['proprietaires']) and row['proprietaires'] != '[]' else []
    locataires = eval(row['locataires']) if pd.notna(row['locataires']) and row['locataires'] != '[]' else []
    charges = eval(row['charges']) if pd.notna(row['charges']) and row['charges'] != '[]' else []
    actifs = eval(row['actifs']) if pd.notna(row['actifs']) and row['actifs'] != '[]' else []
    
    biens.append({
        'bien': row['libelle'],
        'adresse': row['adresse'],
        'type_bien': eval(row['type'])['libelle'] if pd.notna(row['type']) else 'Non défini',
        'nb_proprietaires': len(proprietaires),
        'nb_locataires': len(locataires),
        'total_charges': row['totalCharges'] if pd.notna(row['totalCharges']) else 0,
        'total_loyer': row['totaLoyer'] if pd.notna(row['totaLoyer']) else 0,
        'total_impaye': row['totalImpayer'] if pd.notna(row['totalImpayer']) else 0,
        'nb_actifs': len(actifs)
    })

df_viz = pd.DataFrame(biens)
print(df_viz)

# 6. Visualisation 1: Répartition des biens par type
fig1 = px.pie(df_viz, names='type_bien', title='Répartition des biens par type')
fig1.show()

# 7. Visualisation 2: Loyers par bien
fig2 = px.bar(df_viz.head(15), x='bien', y='total_loyer', 
              title='Loyers par bien (Top 15)',
              labels={'total_loyer': 'Loyer total (FCFA)', 'bien': 'Bien'})
fig2.update_xaxis(tickangle=45)
fig2.show()

# 8. Visualisation 3: Loyer vs Impayés
fig3 = px.scatter(df_viz, x='total_loyer', y='total_impaye', 
                  size='nb_proprietaires', hover_name='bien',
                  title='Loyer vs Impayés (taille = nb propriétaires)',
                  labels={'total_loyer': 'Loyer total (FCFA)', 'total_impaye': 'Impayés (FCFA)'})
fig3.show()

# 9. Visualisation 4: Comparaison charges vs loyers
fig4 = go.Figure()
fig4.add_trace(go.Bar(name='Loyers', x=df_viz['bien'][:10], y=df_viz['total_loyer'][:10]))
fig4.add_trace(go.Bar(name='Charges', x=df_viz['bien'][:10], y=df_viz['total_charges'][:10]))
fig4.update_layout(barmode='group', title='Comparaison Loyers vs Charges (Top 10)',
                   xaxis_title='Bien', yaxis_title='Montant (FCFA)')
fig4.update_xaxis(tickangle=45)
fig4.show()

# 10. Visualisation 5: Analyse des impayés par type de bien
impaye_par_type = df_viz.groupby('type_bien')['total_impaye'].sum().reset_index()
fig5 = px.bar(impaye_par_type, x='type_bien', y='total_impaye',
              title='Total des impayés par type de bien',
              labels={'total_impaye': 'Impayés totaux (FCFA)', 'type_bien': 'Type de bien'})
fig5.show()

# 11. Statistiques descriptives
print("=== STATISTIQUES DESCRIPTIVES ===")
print(f"Nombre total de biens: {len(df_viz)}")
print(f"Revenus locatifs totaux: {df_viz['total_loyer'].sum():,} FCFA")
print(f"Charges totales: {df_viz['total_charges'].sum():,} FCFA")
print(f"Impayés totaux: {df_viz['total_impaye'].sum():,} FCFA")
print(f"Nombre total de propriétaires: {df_viz['nb_proprietaires'].sum()}")
print(f"Nombre total de locataires: {df_viz['nb_locataires'].sum()}")
print("\n=== MOYENNES ===")
print(f"Loyer moyen par bien: {df_viz['total_loyer'].mean():,.2f} FCFA")
print(f"Charges moyennes par bien: {df_viz['total_charges'].mean():,.2f} FCFA")
print(f"Impayés moyens par bien: {df_viz['total_impaye'].mean():,.2f} FCFA")
print(f"Taux d'impayés moyen: {(df_viz['total_impaye'].sum() / df_viz['total_loyer'].sum() * 100):.2f}%")

